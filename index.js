const { Client, Collection, GatewayIntentBits, Partials } = require('discord.js');
const { Player } = require('discord-player');
const { DefaultExtractors } = require('@discord-player/extractor');
require('dotenv').config();
const { YtDlpExtractor } = require('discord-player-ytdlp');


// Validate required environment variables
if (!process.env.token || !process.env.clientId) {
    console.error('❌ Missing required environment variables: token and/or clientId');
    process.exit(1);
}

const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMessageReactions,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.DirectMessages
        // Removed GuildPresences (privileged intent) and GuildWebhooks (not needed)
    ],
    allowedMentions: {
        parse: ['users', 'roles'],
        repliedUser: false
    },
    partials: [
        Partials.Channel,
        Partials.Message,
        Partials.User,
        Partials.GuildMember,
        Partials.Reaction
    ]
});

// Initialize collections
client.slashCommands = new Collection();
client.textCommands = new Collection();
client.buttons = new Collection();
client.selectMenus = new Collection();
client.contextMenus = new Collection();
client.modals = new Collection();

// Store intervals for cleanup
client.intervals = new Set();

/**
 * Initialize the Discord bot with all necessary components
 */
async function initialize() {
    try {
        console.log('🚀 Starting bot initialization...');

        // Initialize player
        const player = new Player(client);
        await player.extractors.register(YtDlpExtractor, {
    ytdlpPath: './bin/yt-dlp.exe', // Path to your yt-dlp binary
    priority: 100,
    enableYouTubeSearch: true,
    enableDirectUrls: true,
    preferYtdlpMetadata: true, // Use yt-dlp for YouTube metadata (default: true)
    streamQuality: 'bestaudio[ext=m4a]/bestaudio[ext=webm]/bestaudio',
  
});

        await player.extractors.loadMulti(DefaultExtractors);
        console.log('✅ Discord Player initialized');

        // Load all handlers with error handling
        const loaders = [
            { name: 'Discord Events', loader: './loaders/discordEvents', param: client, async: false },
            { name: 'Player Events', loader: './loaders/playerEvents', param: player, async: false },
            { name: 'Slash Commands', loader: './loaders/slashCommands', param: client, async: true },
            { name: 'Text Commands', loader: './loaders/textCommands', param: client, async: true },
            { name: 'Buttons', loader: './loaders/buttons', param: client, async: false },
            { name: 'Select Menus', loader: './loaders/selectMenus', param: client, async: false },
            { name: 'Context Menus', loader: './loaders/contextMenus', param: client, async: false },
            { name: 'Modals', loader: './loaders/modals', param: client, async: false }
        ];

        for (const { name, loader, param, async } of loaders) {
            try {
                const loaderFunction = require(loader);
                if (async) {
                    await loaderFunction(param);
                } else {
                    loaderFunction(param);
                }
            } catch (error) {
                console.error(`❌ Failed to load ${name}:`, error.message);
                // Continue loading other components instead of crashing
            }
        }

        // Start bot
        await client.login(process.env.token);
        console.log(`✅ Bot successfully logged in as ${client.user.tag}!`);
    } catch (error) {
        console.error('❌ Initialization failed:', error);
        process.exit(1);
    }
}
/**
 * Graceful shutdown handler
 */
async function gracefulShutdown() {
    console.log('🔄 Shutting down gracefully...');

    // Clear all intervals
    client.intervals.forEach(interval => clearInterval(interval));
    client.intervals.clear();

    // Destroy the client
    if (client.isReady()) {
        await client.destroy();
    }

    console.log('✅ Shutdown complete');
    process.exit(0);
}

// Enhanced error handling
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    // Don't exit on unhandled rejections, just log them
});

process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    gracefulShutdown();
});

// Graceful shutdown on SIGINT and SIGTERM
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// Start the bot
initialize();
