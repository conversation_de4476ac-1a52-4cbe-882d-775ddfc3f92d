const { Client, GatewayIntentBits, EmbedBuilder } = require('discord.js');
const { createAudioPlayer, createAudioResource, joinVoiceChannel, AudioPlayerStatus, VoiceConnectionStatus } = require('@discordjs/voice');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Validate environment variables
if (!process.env.token) {
    console.error('❌ Missing token in .env file');
    process.exit(1);
}

// Create Discord client
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
    ]
});

// Store active connections and players
const connections = new Map();
const players = new Map();

// Path to yt-dlp executable
const YT_DLP_PATH = path.join(__dirname, 'bin', 'yt-dlp.exe');

/**
 * Download audio using yt-dlp
 */
function downloadAudio(url) {
    return new Promise((resolve, reject) => {
        // Check if yt-dlp exists
        if (!fs.existsSync(YT_DLP_PATH)) {
            return reject(new Error('yt-dlp.exe not found in bin/ directory'));
        }

        const outputPath = path.join(__dirname, 'temp', `audio_${Date.now()}.%(ext)s`);
        
        // Ensure temp directory exists
        const tempDir = path.dirname(outputPath);
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }

        const args = [
            '--extract-audio',
            '--audio-format', 'mp3',
            '--audio-quality', '192K',
            '--output', outputPath,
            '--no-playlist',
            url
        ];

        console.log(`🔄 Downloading: ${url}`);
        const ytdlp = spawn(YT_DLP_PATH, args);

        let outputFile = '';
        let errorOutput = '';

        ytdlp.stdout.on('data', (data) => {
            const output = data.toString();
            console.log(output);
            
            // Try to extract the output filename
            const match = output.match(/\[download\] Destination: (.+)/);
            if (match) {
                outputFile = match[1].replace(/\.\w+$/, '.mp3');
            }
        });

        ytdlp.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });

        ytdlp.on('close', (code) => {
            if (code === 0) {
                // Find the actual output file
                if (!outputFile) {
                    const files = fs.readdirSync(tempDir).filter(f => f.startsWith('audio_'));
                    if (files.length > 0) {
                        outputFile = path.join(tempDir, files[0]);
                    }
                }
                
                if (outputFile && fs.existsSync(outputFile)) {
                    resolve(outputFile);
                } else {
                    reject(new Error('Downloaded file not found'));
                }
            } else {
                reject(new Error(`yt-dlp failed with code ${code}: ${errorOutput}`));
            }
        });

        ytdlp.on('error', (error) => {
            reject(new Error(`Failed to spawn yt-dlp: ${error.message}`));
        });
    });
}

/**
 * Play audio in voice channel
 */
async function playAudio(message, url) {
    const voiceChannel = message.member.voice.channel;
    
    if (!voiceChannel) {
        return message.reply('❌ You need to be in a voice channel!');
    }

    const permissions = voiceChannel.permissionsFor(message.client.user);
    if (!permissions.has('Connect') || !permissions.has('Speak')) {
        return message.reply('❌ I need permissions to join and speak in your voice channel!');
    }

    try {
        // Send initial message
        const statusMessage = await message.reply('🔄 Downloading audio...');

        // Download audio
        const audioFile = await downloadAudio(url);
        
        await statusMessage.edit('🔄 Joining voice channel...');

        // Join voice channel
        const connection = joinVoiceChannel({
            channelId: voiceChannel.id,
            guildId: message.guild.id,
            adapterCreator: message.guild.voiceAdapterCreator,
        });

        connections.set(message.guild.id, connection);

        // Wait for connection to be ready
        await new Promise((resolve, reject) => {
            connection.on(VoiceConnectionStatus.Ready, resolve);
            connection.on(VoiceConnectionStatus.Disconnected, () => {
                reject(new Error('Failed to connect to voice channel'));
            });
            setTimeout(() => reject(new Error('Connection timeout')), 10000);
        });

        // Create audio player
        const player = createAudioPlayer();
        players.set(message.guild.id, player);

        // Create audio resource
        const resource = createAudioResource(audioFile);
        
        // Play audio
        player.play(resource);
        connection.subscribe(player);

        await statusMessage.edit('🎵 Now playing!');

        // Handle player events
        player.on(AudioPlayerStatus.Playing, () => {
            console.log('🎵 Audio is playing');
        });

        player.on(AudioPlayerStatus.Idle, () => {
            console.log('⏹️ Audio finished');
            
            // Clean up
            setTimeout(() => {
                if (fs.existsSync(audioFile)) {
                    fs.unlinkSync(audioFile);
                    console.log('🗑️ Cleaned up audio file');
                }
            }, 1000);
        });

        player.on('error', (error) => {
            console.error('❌ Audio player error:', error);
            message.channel.send('❌ An error occurred while playing audio.');
            
            // Clean up
            if (fs.existsSync(audioFile)) {
                fs.unlinkSync(audioFile);
            }
        });

    } catch (error) {
        console.error('❌ Play command error:', error);
        message.reply(`❌ Error: ${error.message}`);
    }
}

// Bot ready event
client.once('ready', () => {
    console.log(`✅ Bot is ready! Logged in as ${client.user.tag}`);
    client.user.setActivity('!play <url>', { type: 'LISTENING' });
});

// Message handler
client.on('messageCreate', async (message) => {
    if (message.author.bot || !message.content.startsWith('!')) return;

    const args = message.content.slice(1).trim().split(/ +/);
    const command = args.shift().toLowerCase();

    if (command === 'play') {
        const url = args[0];
        
        if (!url) {
            return message.reply('❌ Please provide a YouTube URL!\nUsage: `!play <url>`');
        }

        // Basic URL validation
        if (!url.includes('youtube.com') && !url.includes('youtu.be')) {
            return message.reply('❌ Please provide a valid YouTube URL!');
        }

        await playAudio(message, url);
    }
});

// Error handling
client.on('error', (error) => {
    console.error('❌ Discord client error:', error);
});

process.on('unhandledRejection', (error) => {
    console.error('❌ Unhandled rejection:', error);
});

// Login
client.login(process.env.token);
