const { Client, GatewayIntentBits, EmbedBuilder } = require('discord.js');
const { createAudioPlayer, createAudioResource, joinVoiceChannel, AudioPlayerStatus, VoiceConnectionStatus } = require('@discordjs/voice');
const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Validate environment variables
if (!process.env.token) {
    console.error('❌ Missing token in .env file');
    process.exit(1);
}

// Create Discord client
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent
    ]
});

// Store active connections, players, and processes
const connections = new Map();
const players = new Map();
const ytdlpProcesses = new Map();

// Path to yt-dlp executable
const YT_DLP_PATH = path.join(__dirname, 'bin', 'yt-dlp.exe');

/**
 * Get video info using yt-dlp
 */
function getVideoInfo(url) {
    return new Promise((resolve, reject) => {
        // Check if yt-dlp exists
        if (!fs.existsSync(YT_DLP_PATH)) {
            return reject(new Error('yt-dlp.exe not found in bin/ directory'));
        }

        const args = [
            '--print', 'title',
            '--print', 'duration',
            '--print', 'uploader',
            '--no-playlist',
            url
        ];

        console.log(`� Getting video info: ${url}`);
        const ytdlp = spawn(YT_DLP_PATH, args);

        let output = '';
        let errorOutput = '';

        ytdlp.stdout.on('data', (data) => {
            output += data.toString();
        });

        ytdlp.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });

        ytdlp.on('close', (code) => {
            if (code === 0) {
                const lines = output.trim().split('\n');
                resolve({
                    title: lines[0] || 'Unknown Title',
                    duration: lines[1] || 'Unknown Duration',
                    uploader: lines[2] || 'Unknown Uploader'
                });
            } else {
                reject(new Error(`yt-dlp failed with code ${code}: ${errorOutput}`));
            }
        });

        ytdlp.on('error', (error) => {
            reject(new Error(`Failed to spawn yt-dlp: ${error.message}`));
        });
    });
}

/**
 * Create streaming audio resource using yt-dlp
 */
function createStreamingResource(url) {
    return new Promise((resolve, reject) => {
        // Check if yt-dlp exists
        if (!fs.existsSync(YT_DLP_PATH)) {
            return reject(new Error('yt-dlp.exe not found in bin/ directory'));
        }

        const args = [
            '--format', 'bestaudio[ext=webm]/bestaudio/best',
            '--output', '-',
            '--no-playlist',
            '--quiet',
            url
        ];

        console.log(`🎵 Starting stream: ${url}`);
        const ytdlp = spawn(YT_DLP_PATH, args);

        // Create audio resource from the stream
        const resource = createAudioResource(ytdlp.stdout, {
            inputType: 'webm/opus'
        });

        ytdlp.stderr.on('data', (data) => {
            console.error('yt-dlp stderr:', data.toString());
        });

        ytdlp.on('error', (error) => {
            reject(new Error(`Failed to spawn yt-dlp: ${error.message}`));
        });

        resolve({ resource, process: ytdlp });
    });
}

/**
 * Play audio in voice channel
 */
async function playAudio(message, url) {
    const voiceChannel = message.member.voice.channel;

    if (!voiceChannel) {
        return message.reply('❌ You need to be in a voice channel!');
    }

    const permissions = voiceChannel.permissionsFor(message.client.user);
    if (!permissions.has('Connect') || !permissions.has('Speak')) {
        return message.reply('❌ I need permissions to join and speak in your voice channel!');
    }

    try {
        // Send initial message
        const statusMessage = await message.reply('� Getting video info...');

        // Get video information
        const videoInfo = await getVideoInfo(url);

        await statusMessage.edit('🔄 Joining voice channel...');

        // Join voice channel
        const connection = joinVoiceChannel({
            channelId: voiceChannel.id,
            guildId: message.guild.id,
            adapterCreator: message.guild.voiceAdapterCreator,
        });

        connections.set(message.guild.id, connection);

        // Wait for connection to be ready
        await new Promise((resolve, reject) => {
            connection.on(VoiceConnectionStatus.Ready, resolve);
            connection.on(VoiceConnectionStatus.Disconnected, () => {
                reject(new Error('Failed to connect to voice channel'));
            });
            setTimeout(() => reject(new Error('Connection timeout')), 10000);
        });

        await statusMessage.edit('🎵 Starting stream...');

        // Create streaming resource
        const { resource, process: ytdlpProcess } = await createStreamingResource(url);

        // Store the process for cleanup
        ytdlpProcesses.set(message.guild.id, ytdlpProcess);

        // Create audio player
        const player = createAudioPlayer();
        players.set(message.guild.id, player);

        // Play audio
        player.play(resource);
        connection.subscribe(player);

        // Create embed with video info
        const embed = new EmbedBuilder()
            .setTitle('🎵 Now Streaming')
            .setDescription(`**${videoInfo.title}**`)
            .addFields(
                { name: '⏱️ Duration', value: videoInfo.duration, inline: true },
                { name: '👤 Uploader', value: videoInfo.uploader, inline: true },
                { name: '🔗 URL', value: url, inline: false }
            )
            .setColor('#00FF00')
            .setTimestamp();

        await statusMessage.edit({ content: '', embeds: [embed] });

        // Handle player events
        player.on(AudioPlayerStatus.Playing, () => {
            console.log('🎵 Audio is streaming');
        });

        player.on(AudioPlayerStatus.Idle, () => {
            console.log('⏹️ Stream finished');

            // Clean up yt-dlp process
            const process = ytdlpProcesses.get(message.guild.id);
            if (process && !process.killed) {
                process.kill();
                ytdlpProcesses.delete(message.guild.id);
                console.log('🗑️ Cleaned up yt-dlp process');
            }
        });

        player.on('error', (error) => {
            console.error('❌ Audio player error:', error);
            message.channel.send('❌ An error occurred while streaming audio.');

            // Clean up yt-dlp process
            const process = ytdlpProcesses.get(message.guild.id);
            if (process && !process.killed) {
                process.kill();
                ytdlpProcesses.delete(message.guild.id);
            }
        });

        // Handle yt-dlp process exit
        ytdlpProcess.on('exit', (code) => {
            if (code !== 0) {
                console.error(`❌ yt-dlp process exited with code ${code}`);
            }
            ytdlpProcesses.delete(message.guild.id);
        });

    } catch (error) {
        console.error('❌ Play command error:', error);
        message.reply(`❌ Error: ${error.message}`);
    }
}

// Bot ready event
client.once('ready', () => {
    console.log(`✅ Bot is ready! Logged in as ${client.user.tag}`);
    client.user.setActivity('!play <url>', { type: 'LISTENING' });
});

/**
 * Stop playing and clean up
 */
function stopAudio(guildId) {
    // Stop player
    const player = players.get(guildId);
    if (player) {
        player.stop();
        players.delete(guildId);
    }

    // Disconnect from voice channel
    const connection = connections.get(guildId);
    if (connection) {
        connection.destroy();
        connections.delete(guildId);
    }

    // Kill yt-dlp process
    const process = ytdlpProcesses.get(guildId);
    if (process && !process.killed) {
        process.kill();
        ytdlpProcesses.delete(guildId);
    }
}

/**
 * Clean up all resources
 */
function cleanup() {
    console.log('🔄 Cleaning up resources...');

    // Stop all players and connections
    for (const guildId of players.keys()) {
        stopAudio(guildId);
    }

    console.log('✅ Cleanup complete');
}

// Message handler
client.on('messageCreate', async (message) => {
    if (message.author.bot || !message.content.startsWith('!')) return;

    const args = message.content.slice(1).trim().split(/ +/);
    const command = args.shift().toLowerCase();

    if (command === 'play') {
        const url = args[0];

        if (!url) {
            return message.reply('❌ Please provide a YouTube URL!\nUsage: `!play <url>`');
        }

        // Basic URL validation
        if (!url.includes('youtube.com') && !url.includes('youtu.be')) {
            return message.reply('❌ Please provide a valid YouTube URL!');
        }

        await playAudio(message, url);
    } else if (command === 'stop') {
        const guildId = message.guild.id;

        if (!players.has(guildId) && !connections.has(guildId)) {
            return message.reply('❌ Nothing is currently playing!');
        }

        stopAudio(guildId);
        message.reply('⏹️ Stopped playing and left voice channel.');
    }
});

// Error handling
client.on('error', (error) => {
    console.error('❌ Discord client error:', error);
});

process.on('unhandledRejection', (error) => {
    console.error('❌ Unhandled rejection:', error);
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🔄 Received SIGINT, shutting down gracefully...');
    cleanup();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🔄 Received SIGTERM, shutting down gracefully...');
    cleanup();
    process.exit(0);
});

// Login
client.login(process.env.token);
